# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _


@frappe.whitelist()
def get(chart_name=None, filters=None, **kwargs):
    """
    Get milestone data for projects showing total milestones vs completed milestones.
    Data source depends on project contract type:
    - Built Up: P Schedules and P Schedules Table
    - Item Rate: BOQ and BOQ Items Table
    """

    # Parse filters
    filters = frappe.parse_json(filters) if filters else {}
    project_filter = filters.get('project')

    # Build project filters
    project_filters = {"docstatus": ["<", 2]}
    if project_filter:
        project_filters["name"] = project_filter

    # Get projects based on filter
    projects = frappe.get_all(
        "Projects",
        fields=["name", "project_name", "contract_type"],
        filters=project_filters
    )
    
    total_milestones = 0
    completed_milestones = 0
    project_data = []
    
    for project in projects:
        project_total = 0
        project_completed = 0
        
        if project.contract_type == "Built Up":
            # Get milestones from P Schedules
            p_schedules = frappe.get_all(
                "P Schedules",
                filters={"project": project.name, "docstatus": ["<", 2]},
                fields=["name"]
            )
            
            for schedule in p_schedules:
                # Get milestone items from P Schedules Table
                milestones = frappe.get_all(
                    "P Schedules Table",
                    filters={"parent": schedule.name},
                    fields=["status"]
                )
                
                for milestone in milestones:
                    project_total += 1
                    if milestone.status == "Completed":
                        project_completed += 1
                        
        elif project.contract_type == "Item Rate":
            # Get milestones from BOQ
            boqs = frappe.get_all(
                "BOQ",
                filters={"project": project.name, "docstatus": ["<", 2]},
                fields=["name"]
            )
            
            for boq in boqs:
                # Get milestone items from BOQ Items Table
                milestones = frappe.get_all(
                    "BOQ Items Table",
                    filters={"parent": boq.name},
                    fields=["status"]
                )
                
                for milestone in milestones:
                    project_total += 1
                    if milestone.status == "Completed":
                        project_completed += 1
        
        # Add to totals
        total_milestones += project_total
        completed_milestones += project_completed
        
        # Store project-wise data for potential future use
        if project_total > 0:  # Only include projects with milestones
            project_data.append({
                "project": project.project_name,
                "total": project_total,
                "completed": project_completed,
                "pending": project_total - project_completed
            })
    
    # Prepare project-wise chart data
    labels = []
    total_values = []
    completed_values = []

    for project in project_data:
        labels.append(project["project"])
        total_values.append(project["total"])
        completed_values.append(project["completed"])

    # Return data in the format expected by Frappe charts
    return {
        "labels": labels,
        "datasets": [
            {
                "name": "Total Milestones",
                "values": total_values,
                "chartType": "bar"
            },
            {
                "name": "Completed Milestones",
                "values": completed_values,
                "chartType": "bar"
            }
        ],
        "type": "bar",
        "colors": ["#FFB6C1", "#98FB98"],  # Pastel colors as per user preference
        "summary": {
            "filtered_project": project_filter,
            "total_projects": len(project_data),
            "total_milestones": total_milestones,
            "completed_milestones": completed_milestones,
            "pending_milestones": total_milestones - completed_milestones,
            "completion_percentage": round((completed_milestones / total_milestones * 100), 2) if total_milestones > 0 else 0
        },
        "project_wise_data": project_data
    }

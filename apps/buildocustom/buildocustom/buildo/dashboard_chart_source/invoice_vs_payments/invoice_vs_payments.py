# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt, getdate, add_months, get_first_day, get_last_day
from datetime import datetime, timedelta
import calendar


@frappe.whitelist()
def get_data(chart_name=None, filters=None, from_date=None, to_date=None, **kwargs):
	"""Get data for Invoice vs Payments dashboard chart"""
	
	# Parse filters
	filters = frappe.parse_json(filters) if filters else {}
	
	# Set default date range if not provided
	if not from_date:
		from_date = filters.get('from_date') or add_months(getdate(), -12)
	if not to_date:
		to_date = filters.get('to_date') or getdate()
	
	from_date = getdate(from_date)
	to_date = getdate(to_date)
	
	# Get project and customer filters
	project_filter = filters.get('project')
	customer_filter = filters.get('customer')
	
	# Build filter conditions
	invoice_conditions = ["ci.date >= %s", "ci.date <= %s"]
	payment_conditions = ["pr.date >= %s", "pr.date <= %s"]
	invoice_values = [from_date, to_date]
	payment_values = [from_date, to_date]
	
	if project_filter:
		invoice_conditions.append("ci.project = %s")
		payment_conditions.append("pr.project = %s")
		invoice_values.append(project_filter)
		payment_values.append(project_filter)
	
	if customer_filter:
		invoice_conditions.append("ci.customer = %s")
		payment_conditions.append("pr.customer = %s")
		invoice_values.append(customer_filter)
		payment_values.append(customer_filter)
	
	# Get invoice data grouped by month
	invoice_query = """
		SELECT
			DATE_FORMAT(ci.date, %s) as period,
			SUM(ci.net_payable) as amount
		FROM `tabClient Invoice` ci
		WHERE {conditions}
		GROUP BY DATE_FORMAT(ci.date, %s)
		ORDER BY period
	""".format(conditions=' AND '.join(invoice_conditions))

	# Get payment data grouped by month
	payment_query = """
		SELECT
			DATE_FORMAT(pr.date, %s) as period,
			SUM(pr.amount) as amount
		FROM `tabPayment Received` pr
		WHERE {conditions}
		GROUP BY DATE_FORMAT(pr.date, %s)
		ORDER BY period
	""".format(conditions=' AND '.join(payment_conditions))
	
	# Add date format parameters
	date_format = '%Y-%m'
	invoice_params = [date_format] + invoice_values + [date_format]
	payment_params = [date_format] + payment_values + [date_format]

	invoice_data = frappe.db.sql(invoice_query, invoice_params, as_dict=True)
	payment_data = frappe.db.sql(payment_query, payment_params, as_dict=True)
	
	# Create a comprehensive period list
	periods = set()
	invoice_dict = {}
	payment_dict = {}
	
	for row in invoice_data:
		periods.add(row.period)
		invoice_dict[row.period] = flt(row.amount)
	
	for row in payment_data:
		periods.add(row.period)
		payment_dict[row.period] = flt(row.amount)
	
	# Generate all months between from_date and to_date
	current_date = get_first_day(from_date)
	end_date = get_last_day(to_date)
	
	while current_date <= end_date:
		period = current_date.strftime('%Y-%m')
		periods.add(period)
		current_date = add_months(current_date, 1)
	
	# Sort periods
	sorted_periods = sorted(list(periods))
	
	# Prepare chart data
	labels = []
	invoice_values = []
	payment_values = []
	
	for period in sorted_periods:
		# Format label as "MMM YYYY"
		year, month = period.split('-')
		month_name = calendar.month_abbr[int(month)]
		labels.append(f"{month_name} {year}")
		
		invoice_values.append(invoice_dict.get(period, 0))
		payment_values.append(payment_dict.get(period, 0))
	
	# Return data in the format expected by Frappe charts
	return {
		"labels": labels,
		"datasets": [
			{
				"name": "Client Invoices",
				"values": invoice_values,
				"chartType": "bar"
			},
			{
				"name": "Payments Received", 
				"values": payment_values,
				"chartType": "bar"
			}
		],
		"type": "bar"
	}
